# 北交所意向承租方维度表字段逻辑说明（修正版）

## 历史成交相关字段

| 字段中文名 | 字段英文名 | 数据来源表 | 关键字段 | 逻辑说明 |
|-----------|-----------|-----------|----------|----------|
| 是否成交过房屋出租项目 | is_his_house_prj | dws_biz_hl.dws_wi_biz_houselease_project_d | project_id | MAX(CASE WHEN is_deal = 1 THEN 1 ELSE 0 END) |
| 历史成交项目数量 | his_deal_prj_cnt | dws_biz_hl.dws_wi_biz_houselease_project_d | project_id | COUNT(DISTINCT CASE WHEN is_deal = 1 THEN project_id END) |
| 历史平均成交租金价格 | his_deal_price_avg | dws_biz_hl.dws_wi_biz_houselease_project_d | deal_price_std_sq_d | AVG(CASE WHEN is_deal = 1 THEN deal_price_std_sq_d END) |
| 历史成交租金总额 | his_deal_price_total | dws_biz_hl.dws_wi_biz_houselease_project_d | deal_total_price | SUM(CASE WHEN is_deal = 1 THEN deal_total_price END) |
| 历史成交的房屋类项目数量 | his_deal_house_prj_cnt | dws_biz_hl.dws_wi_biz_houselease_project_d | project_id | COUNT(DISTINCT CASE WHEN is_deal = 1 AND asset_cate = '房屋' THEN project_id END) |
| 历史成交的土地类项目数量 | his_deal_land_prj_cnt | dws_biz_hl.dws_wi_biz_houselease_project_d | project_id | COUNT(DISTINCT CASE WHEN is_deal = 1 AND asset_cate = '土地' THEN project_id END) |
| 历史单项目成交租金价格_最大值 | his_deal_price_max | dws_biz_hl.dws_wi_biz_houselease_project_d | deal_price_std_sq_d | MAX(CASE WHEN is_deal = 1 THEN deal_price_std_sq_d END) |
| 历史单项目成交租金总额_最大值 | his_deal_total_price_max | dws_biz_hl.dws_wi_biz_houselease_project_d | deal_total_price | MAX(CASE WHEN is_deal = 1 THEN deal_total_price END) |
| 历史成交项目最多的省-市-区 | his_deal_district_most | dws_biz_hl.dws_wi_biz_houselease_project_d | province_house、city_house、district_house | 按承租方和省市区分组统计项目数，取项目数最多的省市区（使用FIRST_VALUE窗口函数按项目数倒序） |
| 历史成交项目最多的商圈 | his_deal_bizscop_most | dws_biz_hl.dws_wi_biz_houselease_project_d | biz_district_house | 按承租方和商圈分组统计项目数，取项目数最多的商圈（使用FIRST_VALUE窗口函数按项目数倒序） |
| 历史成交项目最多的详细地址 | his_deal_address_most | dws_biz_hl.dws_wi_biz_houselease_project_d | dt_address_house | 按承租方和详细地址分组统计项目数，取项目数最多的详细地址（使用FIRST_VALUE窗口函数按项目数倒序） |

## 最近一次成交相关字段

| 字段中文名 | 字段英文名 | 数据来源表 | 关键字段 | 逻辑说明 |
|-----------|-----------|-----------|----------|----------|
| 最近一次成交项目名称 | last_deal_prj_nm | dws_biz_hl.dws_wi_biz_houselease_project_d | project_id | 按成交日期倒序取最新记录的project_id（如需project_name请关联项目表） |
| 最近一次成交日期 | last_deal_date | dws_biz_hl.dws_wi_biz_houselease_project_d | deal_date | 按成交日期倒序取最新记录的deal_date |
| 最近一次成交距今天数 | last_deal_since_days | dws_biz_hl.dws_wi_biz_houselease_project_d | deal_date | DATEDIFF(CURRENT_DATE(), deal_date) |
| 最近一次成交项目的资产类型 | last_deal_asset_cate | dws_biz_hl.dws_wi_biz_houselease_project_d | asset_cate | 按成交日期倒序取最新记录的asset_cate |
| 最近一次成交租金价格 | last_deal_price | dws_biz_hl.dws_wi_biz_houselease_project_d | deal_price_std_sq_d | 按成交日期倒序取最新记录的deal_price_std_sq_d |
| 最近一次成交的出租面积 | last_deal_area | dws_biz_hl.dws_wi_biz_houselease_project_d | lease_area | 按成交日期倒序取最新记录的lease_area |
| 最近一次成交的租赁期 | last_deal_period | dws_biz_hl.dws_wi_biz_houselease_project_d | lease_prd_std_d | 按成交日期倒序取最新记录的lease_prd_std_d |
| 最近一次成交项目的房屋坐落位置 | last_deal_location | dws_biz_hl.dws_wi_biz_houselease_project_d | dt_address_house | 按成交日期倒序取最新记录的dt_address_house |
| 最近一次成交项目的房屋所在省-市-区 | last_deal_district | dws_biz_hl.dws_wi_biz_houselease_project_d | province_house、city_house、district_house | 按成交日期倒序取最新记录，拼接为"省-市-区"格式 |
| 最近一次成交项目的房屋所在商圈 | last_deal_bizscop | dws_biz_hl.dws_wi_biz_houselease_project_d | biz_district_house | 按成交日期倒序取最新记录的biz_district_house |
| 最近一次成交项目的房屋所在楼宇 | last_deal_address | dws_biz_hl.dws_wi_biz_houselease_project_d | - | 设为NULL（如有具体楼宇字段请替换） |

## 历史报价相关字段

| 字段中文名 | 字段英文名 | 数据来源表 | 关键字段 | 逻辑说明 |
|-----------|-----------|-----------|----------|----------|
| 历史报价过的项目数量 | his_bid_prj_cnt | dws_biz_hl.dws_wi_biz_houselease_project_d | project_id | COUNT(DISTINCT project_id) |
| 历史报价过的房屋类项目数量 | his_bid_house_cate_cnt | dws_biz_hl.dws_wi_biz_houselease_project_d | project_id | COUNT(DISTINCT CASE WHEN asset_cate = '房屋' THEN project_id END) |
| 历史报价过的土地类项目数量 | his_bid_land_cate_cnt | dws_biz_hl.dws_wi_biz_houselease_project_d | project_id | COUNT(DISTINCT CASE WHEN asset_cate = '土地' THEN project_id END) |
| 历史报价最多的房屋所在省-市-区 | his_bid_district_most | dws_biz_hl.dws_wi_biz_houselease_project_d | province_house、city_house、district_house | 按承租方和省市区分组统计项目数，取项目数最多的省市区（使用FIRST_VALUE窗口函数按项目数倒序） |
| 历史报价最多的房屋所在商圈 | his_bid_bizscop_most | dws_biz_hl.dws_wi_biz_houselease_project_d | biz_district_house | 按承租方和商圈分组统计项目数，取项目数最多的商圈（使用FIRST_VALUE窗口函数按项目数倒序） |

## 最近一次报价相关字段

| 字段中文名 | 字段英文名 | 数据来源表 | 关键字段 | 逻辑说明 |
|-----------|-----------|-----------|----------|----------|
| 最近一次报价的日期 | last_bid_date | dws_biz_hl.dws_wi_biz_houselease_project_d | deal_date | 按日期倒序取最新记录的deal_date（如有专门报价日期字段请替换） |
| 最近一次报价的项目名称 | last_bid_prj_nm | dws_biz_hl.dws_wi_biz_houselease_project_d | project_id | 按日期倒序取最新记录的project_id（如需project_name请关联项目表） |
| 最近一次报价的项目编号 | last_bid_prj_id | dws_biz_hl.dws_wi_biz_houselease_project_d | project_id | 按日期倒序取最新记录的project_id |
| 最近一次报价的资产类型 | last_bid_asset_cate | dws_biz_hl.dws_wi_biz_houselease_project_d | asset_cate | 按日期倒序取最新记录的asset_cate |
| 最近一次报价的租金价格 | last_bid_prj_price | dws_biz_hl.dws_wi_biz_houselease_project_d | list_price_std_sq_d | 按日期倒序取最新记录的list_price_std_sq_d（项目挂牌价格） |
| 最近一次报价的租金总额 | last_bid_prj_total_price | dws_biz_hl.dws_wi_biz_houselease_project_d | list_total_price_cal | 按日期倒序取最新记录的list_total_price_cal（项目挂牌总价） |
| 最近一次报价的地区_省-市-区 | last_bid_district | dws_biz_hl.dws_wi_biz_houselease_project_d | province_house、city_house、district_house | 按日期倒序取最新记录，拼接为"省-市-区"格式 |
| 最近一次报价的地区_商圈 | last_bid_bizscop | dws_biz_hl.dws_wi_biz_houselease_project_d | biz_district_house | 按日期倒序取最新记录的biz_district_house |

## 统计指标字段

| 字段中文名 | 字段英文名 | 数据来源表 | 关键字段 | 逻辑说明 |
|-----------|-----------|-----------|----------|----------|
| 历史报价成交率 | his_bid_deal_rate | dws_biz_hl.dws_wi_biz_houselease_project_d | - | COALESCE(成交次数 / NULLIF(报价总次数, 0), 0) |
| 历史报价在项目总报价中排名前三的次数 | his_bid_top3_num | dws_biz_hl.dws_wi_biz_houselease_project_d | - | SUM(CASE WHEN is_deal = 1 OR is_top3_bidder_only_rank = 1 THEN 1 ELSE 0 END) |
| 历史报价在项目总报价中排名前三的次数占比 | his_bid_top3_rate | dws_biz_hl.dws_wi_biz_houselease_project_d | - | COALESCE(前三次数 / NULLIF(报价总次数, 0), 0) |

## 主要修正内容

1. **修正了历史平均成交租金价格计算**：使用AVG函数而不是手动计算
2. **修正了最大值字段**：使用正确的成交价格字段而不是挂牌价格字段
3. **完善了"最多"逻辑**：使用窗口函数实现真正的统计排序逻辑
4. **统一了字段映射**：明确了location_house和dt_address_house的使用
5. **完善了报价相关逻辑**：参照成交逻辑实现了完整的报价统计
6. **优化了SQL结构**：分离了地区统计逻辑，提高了代码可读性和性能
