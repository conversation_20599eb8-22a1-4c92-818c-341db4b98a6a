WITH LesseeBase AS (
    -- 基础数据：筛选出意向承租方相关的所有记录
    SELECT
        project_id, -- 项目ID
        project_name, -- 项目名称
        intnt_rent_id, -- 承租方ID
        intnt_rent_nm, -- 承租方名称
        list_price_std_sq_d, -- 项目挂牌价格_标准化（元/平方米/天）
        list_total_price_cal -- 项目挂牌总价_计算值（元）
    FROM
        dws_wi_biz_houselease_lessee_d
    where dt = '${dmp_day}'
),
--互联app行为数据
with sys_pnm as (
    -- 取最近时间一年内的
    select 
    cust_no, -- 客户号
    sum(case when spec_type = 'GL' and bhvr_tp = '10' and spec_code != '' then 1 else 0 end) as last_year_view_prj_cnt, -- 最近一年浏览的项目数量
    sum(case when spec_type = 'GL' and bhvr_tp = '15' and spec_code != '' then 1 else 0 end) as last_year_follow_prj_cnt, -- 最近一年浏览的项目数量
    sum(case when spec_type = 'GL' and bhvr_tp = '25' and spec_code != '' then 1 else 0 end) as last_year_share_prj_cnt, -- 最近一年分享的项目数量
    max(last_active_date) as last_active_date -- 最近一次活跃日期
    FROM
std.std_bjhl_ctxn_sys_pnm_log_d
 where dt = '${dmp_day}'
 and col_tm >= date_sub(current_date,365) -- 取最近时间一年内的记录
)